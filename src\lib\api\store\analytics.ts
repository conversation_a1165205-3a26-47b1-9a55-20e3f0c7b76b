import { supabase } from '@/lib/supabase';

/**
 * Landing Page Analytics API for Store Management
 * REDUCED SCOPE: Basic performance alerts and recommendations only
 */

// Types for basic analytics
export interface BasicAnalytics {
  id: string;
  store_id: string;
  page_views: number;
  unique_visitors: number;
  bounce_rate: number;
  avg_time_on_page: number;
  chat_button_clicks: number;
  carousel_interactions: number;
  banner_clicks: number;
  date: string;
  created_at: string;
}

export interface PerformanceAlert {
  type: 'warning' | 'info' | 'success';
  title: string;
  message: string;
  recommendation?: string;
  priority: 'low' | 'medium' | 'high';
}

export interface BasicRecommendation {
  category: 'content' | 'engagement' | 'performance';
  title: string;
  description: string;
  action: string;
  impact: 'low' | 'medium' | 'high';
}

export interface AnalyticsSummary {
  totalPageViews: number;
  totalUniqueVisitors: number;
  averageBounceRate: number;
  averageTimeOnPage: number;
  totalChatClicks: number;
  totalCarouselInteractions: number;
  totalBannerClicks: number;
  period: string;
}

/**
 * Analytics API Class - Basic Performance Alerts Only
 */
export class AnalyticsAPI {
  /**
   * Get basic analytics summary for a store
   */
  static async getAnalyticsSummary(storeId: string, days: number = 30): Promise<AnalyticsSummary> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('store_landing_analytics')
        .select('*')
        .eq('store_id', storeId)
        .gte('date', startDate.toISOString().split('T')[0])
        .order('date', { ascending: false });

      if (error) throw error;

      // Calculate summary from data
      const analytics = data || [];
      const summary: AnalyticsSummary = {
        totalPageViews: analytics.reduce((sum, item) => sum + (item.page_views || 0), 0),
        totalUniqueVisitors: analytics.reduce((sum, item) => sum + (item.unique_visitors || 0), 0),
        averageBounceRate: analytics.length > 0 
          ? analytics.reduce((sum, item) => sum + (item.bounce_rate || 0), 0) / analytics.length 
          : 0,
        averageTimeOnPage: analytics.length > 0 
          ? analytics.reduce((sum, item) => sum + (item.avg_time_on_page || 0), 0) / analytics.length 
          : 0,
        totalChatClicks: analytics.reduce((sum, item) => sum + (item.chat_button_clicks || 0), 0),
        totalCarouselInteractions: analytics.reduce((sum, item) => sum + (item.carousel_interactions || 0), 0),
        totalBannerClicks: analytics.reduce((sum, item) => sum + (item.banner_clicks || 0), 0),
        period: `${days} days`
      };

      return summary;
    } catch (error) {
      console.error('Error fetching analytics summary:', error);
      // Return empty summary on error
      return {
        totalPageViews: 0,
        totalUniqueVisitors: 0,
        averageBounceRate: 0,
        averageTimeOnPage: 0,
        totalChatClicks: 0,
        totalCarouselInteractions: 0,
        totalBannerClicks: 0,
        period: `${days} days`
      };
    }
  }

  /**
   * Generate basic performance alerts based on analytics data
   */
  static async getPerformanceAlerts(storeId: string): Promise<PerformanceAlert[]> {
    try {
      const summary = await this.getAnalyticsSummary(storeId, 30);
      const alerts: PerformanceAlert[] = [];

      // High bounce rate alert
      if (summary.averageBounceRate > 70) {
        alerts.push({
          type: 'warning',
          title: 'High Bounce Rate',
          message: `Your bounce rate is ${summary.averageBounceRate.toFixed(1)}%, which is above the recommended 70%.`,
          recommendation: 'Consider adding more engaging content or improving page load speed.',
          priority: 'high'
        });
      }

      // Low engagement alert
      if (summary.totalChatClicks < 10 && summary.totalPageViews > 100) {
        alerts.push({
          type: 'warning',
          title: 'Low Chat Engagement',
          message: 'Your chat button has low click-through rate.',
          recommendation: 'Try customizing the chat button text or position to make it more appealing.',
          priority: 'medium'
        });
      }

      // Carousel performance alert
      if (summary.totalCarouselInteractions < 5 && summary.totalPageViews > 50) {
        alerts.push({
          type: 'info',
          title: 'Carousel Needs Attention',
          message: 'Your book carousel has low interaction rates.',
          recommendation: 'Consider updating featured books or improving book descriptions.',
          priority: 'medium'
        });
      }

      // Good performance recognition
      if (summary.averageBounceRate < 50 && summary.totalPageViews > 0) {
        alerts.push({
          type: 'success',
          title: 'Great Engagement!',
          message: `Excellent bounce rate of ${summary.averageBounceRate.toFixed(1)}%`,
          recommendation: 'Keep up the good work with your current content strategy.',
          priority: 'low'
        });
      }

      // No data alert
      if (summary.totalPageViews === 0) {
        alerts.push({
          type: 'info',
          title: 'No Analytics Data',
          message: 'No visitor data available for the past 30 days.',
          recommendation: 'Analytics tracking will begin collecting data as visitors use your landing page.',
          priority: 'low'
        });
      }

      return alerts;
    } catch (error) {
      console.error('Error generating performance alerts:', error);
      return [{
        type: 'warning',
        title: 'Analytics Unavailable',
        message: 'Unable to load performance data at this time.',
        recommendation: 'Please try refreshing the page or contact support if the issue persists.',
        priority: 'low'
      }];
    }
  }

  /**
   * Generate basic recommendations for store improvement
   */
  static async getBasicRecommendations(storeId: string): Promise<BasicRecommendation[]> {
    try {
      const summary = await this.getAnalyticsSummary(storeId, 30);
      const recommendations: BasicRecommendation[] = [];

      // Content recommendations
      if (summary.totalPageViews > 0) {
        if (summary.totalCarouselInteractions / summary.totalPageViews < 0.1) {
          recommendations.push({
            category: 'content',
            title: 'Improve Book Carousel',
            description: 'Your carousel has low engagement rates.',
            action: 'Update featured books with popular or trending titles',
            impact: 'medium'
          });
        }

        if (summary.totalBannerClicks / summary.totalPageViews < 0.05) {
          recommendations.push({
            category: 'content',
            title: 'Optimize Promotional Banners',
            description: 'Banner click-through rates could be improved.',
            action: 'Create more compelling banner content or calls-to-action',
            impact: 'medium'
          });
        }
      }

      // Engagement recommendations
      if (summary.averageTimeOnPage < 60) {
        recommendations.push({
          category: 'engagement',
          title: 'Increase Page Engagement',
          description: 'Visitors are spending less than a minute on your page.',
          action: 'Add more interactive content or improve page layout',
          impact: 'high'
        });
      }

      // Performance recommendations
      if (summary.averageBounceRate > 60) {
        recommendations.push({
          category: 'performance',
          title: 'Reduce Bounce Rate',
          description: 'Many visitors leave without exploring your content.',
          action: 'Improve page loading speed and add engaging hero content',
          impact: 'high'
        });
      }

      // Default recommendations for new stores
      if (summary.totalPageViews === 0) {
        recommendations.push({
          category: 'content',
          title: 'Complete Your Setup',
          description: 'Ensure all landing page sections are configured.',
          action: 'Add books to carousel, create banners, and customize hero section',
          impact: 'high'
        });
      }

      return recommendations;
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return [{
        category: 'performance',
        title: 'System Check',
        description: 'Unable to generate recommendations at this time.',
        action: 'Please refresh the page or contact support',
        impact: 'low'
      }];
    }
  }

  /**
   * Check if analytics data exists for a store
   */
  static async hasAnalyticsData(storeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('store_landing_analytics')
        .select('id')
        .eq('store_id', storeId)
        .limit(1);

      if (error) throw error;
      return (data?.length || 0) > 0;
    } catch (error) {
      console.error('Error checking analytics data:', error);
      return false;
    }
  }

  /**
   * Get simple metrics for dashboard display
   */
  static async getSimpleMetrics(storeId: string): Promise<{
    pageViews: number;
    chatClicks: number;
    bounceRate: number;
    hasData: boolean;
  }> {
    try {
      const summary = await this.getAnalyticsSummary(storeId, 7); // Last 7 days
      
      return {
        pageViews: summary.totalPageViews,
        chatClicks: summary.totalChatClicks,
        bounceRate: Math.round(summary.averageBounceRate),
        hasData: summary.totalPageViews > 0
      };
    } catch (error) {
      console.error('Error fetching simple metrics:', error);
      return {
        pageViews: 0,
        chatClicks: 0,
        bounceRate: 0,
        hasData: false
      };
    }
  }
}
